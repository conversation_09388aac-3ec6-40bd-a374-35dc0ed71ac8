# Network Web App

A professional networking platform built with Next.js 15, TypeScript, and Tailwind CSS.

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (dashboard)/        # Dashboard group
│   │   └── dashboard/      # Dashboard pages
│   └── (onboard)/          # Onboarding group
│       └── login/          # Login page
│           ├── page.tsx    # Login page component
│           └── components/ # Login-specific components
│               ├── LoginForm/
│               │   ├── index.tsx
│               │   ├── types.ts
│               │   └── useLoginForm.ts
│               └── SocialLogin/
│                   ├── index.tsx
│                   └── types.ts
├── components/             # Shared UI components
│   ├── Button/
│   ├── Input/
│   └── index.ts
├── constants/              # Configuration and constants
│   ├── config.ts          # App configuration (colors, etc.)
│   └── index.ts
└── utils/                  # Utility functions
    └── index.ts
```

## Features

- **LinkedIn-style Login Page**: Clean, professional design with social login options
- **Custom Base Color**: Configurable brand color (#448600)
- **Form Validation**: Email and password validation with error handling
- **Social Authentication**: Google and Apple sign-in buttons (ready for integration)
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **TypeScript**: Full type safety with custom type definitions
- **Reusable Components**: Modular component architecture

## Getting Started

1. Install dependencies:

```bash
npm install
```

2. Run the development server:

```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) (or the port shown in terminal)

## Configuration

The app uses a centralized configuration system. Update colors and settings in `src/constants/config.ts`:

```typescript
export const CONFIG = {
  baseColor: '#448600',
  colors: {
    primary: '#448600',
    primaryHover: '#3a7300',
    // ... other colors
  },
};
```

## Component Usage

### LoginForm

- Handles both sign-in and sign-up modes
- Built-in form validation
- Password visibility toggle
- Error handling and loading states

### SocialLogin

- Google and Apple authentication buttons
- Consistent styling with brand colors
- Ready for OAuth integration

## Development Guidelines

- Use `type` instead of `interface` for type definitions
- Add `I` suffix to type names (e.g., `LoginFormPropsI`)
- Keep components simple and focused
- Use the custom `cn` utility for conditional classes
- Follow the established folder structure

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
