import NextAuth from 'next-auth';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';
import { getOrGenerateDeviceId } from '@/utils/deviceId';
import { parseAuthError } from '@/utils/errorParser';

export type AuthLoginParamsI = {
  type: 'GOOGLE' | 'EMAIL_PASSWORD';
  email?: string;
  password?: string;
  externalToken?: string;
  deviceToken?: string;
  ip?: string;
  versionNo: string;
  deviceId: string;
  platform: string;
};

export type AuthLoginResultI = {
  username?: string;
  name?: string;
  isUsernameSaved: boolean;
  email: string;
  profileId: string;
  designationText?: string;
  entityText?: string;
  avatar?: string;
  isEmailVerified: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  token: string;
  jwtToken: string;
  isPrivacyPolicyAccepted: boolean;
  previousStatus: string;
};

export async function callBackendLogin(params: any): Promise<any> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/auth/login`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-version-no': params.versionNo,
        'x-device-id': params.deviceId,
        'x-platform': params.platform,
        'x-api-key': process.env.NEXT_PUBLIC_API_KEY || ""
      },
      body: JSON.stringify({
        type: params.type,
        email: params.email,
        password: params.password,
        externalToken: params.externalToken,
        deviceToken: params.deviceToken,
      }),
    }
  );
  const data = await response.json();
  
  if (!data.token) {
    const error = parseAuthError(data);
    throw new Error(error.description || error.message || 'Login failed');
  }

  return data;
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  pages: {
    signIn: '/login',
  },
  providers: [
    Google,
    Credentials({
      credentials: {
        email: {
          type: 'email',
          label: 'Email',
          placeholder: '<EMAIL>',
        },
        password: {
          type: 'password',
          label: 'Password',
          placeholder: '*****',
        },
      },

      authorize: async credentials => {
        try {
          const deviceId = getOrGenerateDeviceId();
          const loginParams: AuthLoginParamsI = {
            type: 'EMAIL_PASSWORD',
            email: credentials.email as string,
            password: credentials.password as string,
            versionNo: process.env.APP_VERSION || '1.0.0',
            deviceId,
            platform: 'web_app',
          };

          const loginResult = await callBackendLogin(loginParams);

          return {
            id: loginResult.profileId,
            email: loginResult.email,
            name: loginResult.name,
            image: loginResult.avatar,
            backendData: loginResult,
            deviceId,
          };
        } catch (error) {
          console.error('Login error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        try {
          const deviceId = getOrGenerateDeviceId();

          const loginParams: AuthLoginParamsI = {
            type: 'GOOGLE',
            email: user.email!,
            externalToken: account.id_token!,
            versionNo: process.env.APP_VERSION || '1.0.0',
            deviceId,
            platform: process.env.PLATFORM || 'web_app',
          };

          const loginResult = await callBackendLogin(loginParams);

          // Store backend data in user object
          user.backendData = loginResult;
          user.deviceId = deviceId;
          user.id = loginResult.profileId;

          return true;
        } catch (error) {
          console.error('Google login backend error:', error);
          return false;
        }
      }

      return true;
    },
    async jwt({ token, user }) {
      if (user?.backendData) {
        const data = user.backendData;

        // Store all backend data in token
        token.backendData = data;
        token.profileId = data.profileId;
        token.username = data.username;
        token.designationText = data.designationText;
        token.entityText = data.entityText;
        token.avatar = data.avatar;
        token.isEmailVerified = data.isEmailVerified;
        token.isUsernameSaved = data.isUsernameSaved;
        token.isPersonalDetailsSaved = data.isPersonalDetailsSaved;
        token.isWorkDetailsSaved = data.isWorkDetailsSaved;
        token.isPrivacyPolicyAccepted = data.isPrivacyPolicyAccepted;
        token.previousStatus = data.previousStatus;
        token.token = data.token;
        token.jwtToken = data.jwtToken;
        token.deviceId = user.deviceId;
      }
      return token;
    },
    async session({ session, token }) {
      // Make backend data available in session
      if (token.backendData) {
        // Update user info
        (session.user as any).id = token.profileId as string;
        (session.user as any).email = token.backendData.email;
        (session.user as any).name = token.backendData.name;
        (session.user as any).image = token.backendData.avatar;

        // Add all backend data to session
        (session as any).profileId = token.profileId;
        (session as any).username = token.username;
        (session as any).designationText = token.designationText;
        (session as any).entityText = token.entityText;
        (session as any).avatar = token.avatar;
        (session as any).isEmailVerified = token.isEmailVerified;
        (session as any).isUsernameSaved = token.isUsernameSaved;
        (session as any).isPersonalDetailsSaved = token.isPersonalDetailsSaved;
        (session as any).isWorkDetailsSaved = token.isWorkDetailsSaved;
        (session as any).isPrivacyPolicyAccepted = token.isPrivacyPolicyAccepted;
        (session as any).previousStatus = token.previousStatus;
        (session as any).token = token.token;
        (session as any).jwtToken = token.jwtToken;
        (session as any).deviceId = token.deviceId;
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 60 * 24 * 60 * 60,
  },
});
