import NextAuth from 'next-auth';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';
import { getOrGenerateDeviceId } from '@/utils/deviceId';
import { parseAuthError } from '@/utils/errorParser';
import { apiCall } from '@/lib/api';
import { AuthLoginResultI } from '@/networks/auth/types';

export type AuthLoginParamsI = {
  type: 'GOOGLE' | 'EMAIL_PASSWORD';
  email?: string;
  password?: string;
  externalToken?: string;
  deviceToken?: string;
  ip?: string;
  versionNo: string;
  deviceId: string;
  platform: string;
};



export async function callBackendLogin(params: AuthLoginParamsI): Promise<AuthLoginResultI> {
  try {

    const loginPayload = {
      type: params.type,
      email: params.email,
      password: params.password,
      externalToken: params.externalToken,
      deviceToken: params.deviceToken,
    };

    const result = await apiCall<typeof loginPayload, AuthLoginResultI>(
      '/backend/api/v1/auth/login',
      'POST',
      {
        payload: loginPayload,
        isAuth: false,
        headers: {
          'x-version-no': params.versionNo,
          'x-device-id': params.deviceId,
          'x-platform': params.platform,
        },
      }
    );

    

    if (!result.token) {
      const error = parseAuthError(result);
      throw new Error(error.description || error.message || 'Login failed');
    }

    return result;
  } catch (error) {
    console.error('❌ Backend login failed:', error);
    const parsedError = parseAuthError(error);
    throw new Error(parsedError.description || parsedError.message || 'Login failed');
  }
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  pages: {
    signIn: '/login',
  },
  providers: [
    Google,
    Credentials({
      credentials: {
        email: {
          type: 'email',
          label: 'Email',
          placeholder: '<EMAIL>',
        },
        password: {
          type: 'password',
          label: 'Password',
          placeholder: '*****',
        },
      },

      authorize: async credentials => {
        try {
          const deviceId = getOrGenerateDeviceId();
          const loginParams: AuthLoginParamsI = {
            type: 'EMAIL_PASSWORD',
            email: credentials.email as string,
            password: credentials.password as string,
            versionNo: process.env.APP_VERSION || '1.0.0',
            deviceId,
            platform: 'web_app',
          };

          const loginResult = await callBackendLogin(loginParams);
          console.log(loginResult, 'loginResult');
          return {
            id: loginResult.profileId,
            email: loginResult.email,
            name: loginResult.name,
            image: loginResult.avatar,
            backendData: loginResult,
            deviceId,
          };
        } catch (error) {
          console.error('Login error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        try {
          const deviceId = getOrGenerateDeviceId();

          const loginParams: AuthLoginParamsI = {
            type: 'GOOGLE',
            email: user.email!,
            externalToken: account.id_token!,
            versionNo: process.env.APP_VERSION || '1.0.0',
            deviceId,
            platform: process.env.PLATFORM || 'web_app',
          };

          const loginResult = await callBackendLogin(loginParams);

          // Store backend data in user object
          user.backendData = loginResult;
          user.deviceId = deviceId;
          user.id = loginResult.profileId;

          return true;
        } catch (error) {
          console.error('Google login backend error:', error);
          return false;
        }
      }

      return true;
    },
    async jwt({ token, user }) {
      if (user?.backendData) {
        const data = user.backendData;

        token.backendData = data;
        token.profileId = data.profileId;
        token.username = data.username;
        token.designationText = data.designationText;
        token.entityText = data.entityText;
        token.avatar = data.avatar;
        token.isEmailVerified = data.isEmailVerified;
        token.isUsernameSaved = data.isUsernameSaved;
        token.isPersonalDetailsSaved = data.isPersonalDetailsSaved;
        token.isWorkDetailsSaved = data.isWorkDetailsSaved;
        token.isPrivacyPolicyAccepted = data.isPrivacyPolicyAccepted;
        token.previousStatus = data.previousStatus;
        token.token = data.token;
        token.jwtToken = data.jwtToken;
        token.deviceId = user.deviceId;
      }
      return token;
    },
    async session({ session, token }) {
      if (token.backendData) {
        // Update user info
        (session.user).id = token.profileId as string;
        (session.user).email = token.backendData.email as string;
        (session.user).name = token.backendData.name as string;
        (session.user).image = token.backendData.avatar;

        // Add all backend data to session
        (session).profileId = token.profileId;
        (session).username = token.username;
        (session).designationText = token.designationText;
        (session).entityText = token.entityText;
        (session).avatar = token.avatar;
        (session).isEmailVerified = token.isEmailVerified;
        (session).isUsernameSaved = token.isUsernameSaved;
        (session).isPersonalDetailsSaved = token.isPersonalDetailsSaved;
        (session).isWorkDetailsSaved = token.isWorkDetailsSaved;
        (session).isPrivacyPolicyAccepted = token.isPrivacyPolicyAccepted;
        (session).previousStatus = token.previousStatus;
        (session).token = token.token;
        (session).jwtToken = token.jwtToken;
        (session).deviceId = token.deviceId;
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 60 * 24 * 60 * 60,
  },
});
