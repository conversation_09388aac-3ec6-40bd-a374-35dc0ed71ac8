import NextAuth from 'next-auth';
// import { authConfig } from './auth.config';
// import Credentials from 'next-auth/providers/credentials';
import { loginAPI } from '@/networks/auth/login';
// import { AuthLoginResultI } from '@/networks/auth/types';
import Google from 'next-auth/providers/google';

// const mapBackendData = (data: AuthLoginResultI) => ({
//   id: data.profileId,
//   name: data.name,
//   email: data.email,
//   image: data.avatar,
//   accessToken: data.token,
//   jwtToken: data.jwtToken,
//   isUsernameSaved: data.isUsernameSaved ?? false,
//   isPersonalDetailsSaved: data.isPersonalDetailsSaved ?? false,
//   isWorkDetailsSaved: data.isWorkDetailsSaved ?? false,
//   isPrivacyPolicyAccepted: data.isPrivacyPolicyAccepted ?? false,
// });

export const {
  handlers,
  auth,
  signIn,
  signOut,
} = NextAuth({
  session: { strategy: 'jwt' },
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    // Credentials({
    //   async authorize(credentials) {
    //     try {
    //       if (!credentials) return null;

    //       const { email, password } = credentials as {
    //         type: string;
    //         email?: string;
    //         password?: string;
    //         externalToken?: string;
    //       };

    //       const loginResponse = await loginAPI({
    //         type: "EMAIL_PASSWORD",
    //         email,
    //         password,
    //       });

    //       if (!loginResponse?.token) throw new Error('Invalid credentials');
    //       return mapBackendData(loginResponse);

    //     } catch (error) {
    //       console.log(error)
    //     }
    //   },
    // }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google' || account?.provider === 'apple') {
        try {
          const type = account.provider === 'google' ? 'GOOGLE' : 'APPLE';

          const loginResponse = await loginAPI({
            type,
            externalToken: account.access_token!,
            email: user.email as string,
          });

          if (loginResponse?.token) {
            user.backendData = loginResponse;
            return true;
          }
          return false;
        } catch (error) {
          console.error('Google OAuth Backend Error:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        const backendData = user.backendData;

        if (backendData) {
          Object.assign(token, user.backendData);
        } else {
          token.id = user.id;
          token.name = user.name;
          token.email = user.email;
          token.picture = user.image;
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.picture as string;
        session.accessToken = token.accessToken as string;
        session.jwtToken = token.jwtToken as string;
        session.isUsernameSaved = token.isUsernameSaved as boolean;
        session.isPersonalDetailsSaved =
          token.isPersonalDetailsSaved as boolean;
        session.isWorkDetailsSaved = token.isWorkDetailsSaved as boolean;
        session.isPrivacyPolicyAccepted =
          token.isPrivacyPolicyAccepted as boolean;
      }
      return session;
    },
  },
});
