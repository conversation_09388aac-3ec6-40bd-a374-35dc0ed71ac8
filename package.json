{"name": "network-web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "dependencies": {"@auth/core": "^0.34.2", "@hookform/resolvers": "^5.1.1", "@react-oauth/google": "^0.12.2", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "next": "15.3.5", "next-auth": "^5.0.0-beta.29", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md,html}": ["prettier --write"]}}