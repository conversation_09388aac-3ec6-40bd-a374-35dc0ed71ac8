import React from 'react';

type AnswerIconPropsI = {
  className?: string;
  size?: number;
};

const AnswerIcon = ({ className = '', size = 16 }: AnswerIconPropsI) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M16.84 2.73c-1.19-.35-2.57-.35-3.76 0C11.66 3.09 10.5 4.25 10.14 5.67c-.18.71-.18 1.46 0 2.17.36 1.42 1.52 2.58 2.94 2.94.71.18 1.46.18 2.17 0 1.42-.36 2.58-1.52 2.94-2.94.18-.71.18-1.46 0-2.17-.36-1.42-1.52-2.58-2.94-2.94zM15 7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
    <path d="M12 2l3.09 6.26L22 9l-5.55 5.41L18.18 22 12 18.77 5.82 22l1.73-7.59L2 9l6.91-.74L12 2z"/>
  </svg>
);

export default AnswerIcon;
