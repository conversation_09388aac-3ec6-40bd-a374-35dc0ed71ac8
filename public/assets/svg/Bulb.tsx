import React from 'react';

interface BulbIconProps {
  size?: number;
  color?: string;
  className?: string;
  onClick?: () => void;
}

const BulbIcon: React.FC<BulbIconProps> = ({
  size = 18,
  color = '#525252',
  className = '',
  onClick,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'inherit' }}
    >
      <path
        d="M6.14286 15.4H10.8571V17H6.14286V15.4ZM12.3547 10.3696C13.164 9.4048 14 8.4072 14 6.6C14 3.5128 11.5321 1 8.5 1C5.46793 1 3 3.5128 3 6.6C3 8.428 3.83836 9.4224 4.65079 10.384C4.93207 10.7184 5.22357 11.0648 5.5025 11.4632C5.61564 11.628 5.80107 12.26 5.96686 13H5.34457V14.6H11.6554V13H11.0347C11.2021 12.2584 11.3883 11.6248 11.5006 11.46C11.7772 11.0576 12.0711 10.708 12.3547 10.3696Z"
        fill={color}
      />
    </svg>
  );
};

export default BulbIcon;