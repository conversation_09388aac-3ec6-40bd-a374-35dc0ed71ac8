import React from 'react';

type DownIconProps = {
  size?: number;
  color?: string;
  className?: string;
  onClick?: () => void;
}

const DownIcon: React.FC<DownIconProps> = ({
  size = 18,
  color = '#525252', // Default gray color
  className = '',
  onClick,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'inherit' }}
    >
      <path
        d="M1.72995 9.34739C1.7725 9.2446 1.84458 9.15673 1.93707 9.09491C2.02956 9.03308 2.1383 9.00007 2.24956 9.00005H5.06206V3.37505C5.06206 3.07668 5.18058 2.79053 5.39156 2.57955C5.60254 2.36857 5.88869 2.25005 6.18706 2.25005L11.8121 2.25005C12.1104 2.25005 12.3966 2.36857 12.6076 2.57955C12.8185 2.79053 12.9371 3.07668 12.9371 3.37505L12.9371 9.00005H15.7496C15.8609 8.99996 15.9697 9.0329 16.0623 9.0947C16.1549 9.15651 16.227 9.24439 16.2697 9.34723C16.3123 9.45006 16.3234 9.56323 16.3017 9.67241C16.2799 9.78158 16.2263 9.88185 16.1475 9.96051L9.39753 16.7105C9.34528 16.7628 9.28325 16.8043 9.21496 16.8326C9.14667 16.8609 9.07348 16.8755 8.99956 16.8755C8.92563 16.8755 8.85244 16.8609 8.78415 16.8326C8.71587 16.8043 8.65383 16.7628 8.60159 16.7105L1.85159 9.96051C1.77295 9.8818 1.71941 9.78154 1.69776 9.6724C1.67611 9.56327 1.68731 9.45016 1.72995 9.34739Z"
        fill={color}
      />
    </svg>
  );
};

export default DownIcon;