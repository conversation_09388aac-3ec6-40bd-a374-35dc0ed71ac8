'use client'

import { FC, SVGProps } from 'react'
import { cn } from '@/utilities' 

interface EyeOffIconProps extends SVGProps<SVGSVGElement> {
  size?: number | string 
  color?: string 
  className?: string 
}

const EyeClosedIcon: FC<EyeOffIconProps> = ({
  size = 24, 
  color = 'black', 
  className,
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
      {...props}
    >
      <path
        d="M21.4885 15.4688C21.5631 15.5983 21.5831 15.7522 21.5443 15.8965C21.5054 16.0408 21.4108 16.1638 21.2813 16.2384C21.1517 16.313 20.9979 16.3331 20.8535 16.2943C20.7092 16.2554 20.5862 16.1608 20.5116 16.0313L18.6366 12.7425C17.5278 13.5198 16.2899 14.094 14.9803 14.4384L15.5588 17.9072C15.571 17.9801 15.5687 18.0547 15.5521 18.1267C15.5355 18.1987 15.5048 18.2667 15.4619 18.3269C15.419 18.387 15.3646 18.4381 15.302 18.4773C15.2393 18.5164 15.1695 18.5428 15.0966 18.555C15.0656 18.5598 15.0342 18.5623 15.0028 18.5625C14.87 18.5624 14.7415 18.5152 14.6401 18.4294C14.5387 18.3436 14.4709 18.2247 14.4488 18.0938L13.875 14.6663C12.6326 14.8614 11.3674 14.8614 10.125 14.6663L9.55503 18.0938C9.53286 18.2249 9.46495 18.3439 9.36335 18.4297C9.26174 18.5156 9.13302 18.5626 9.00003 18.5625C8.96864 18.5623 8.9373 18.5598 8.90628 18.555C8.83337 18.5427 8.7636 18.5162 8.70095 18.477C8.6383 18.4377 8.58401 18.3865 8.54117 18.3262C8.49833 18.266 8.46779 18.1979 8.45129 18.1258C8.43479 18.0538 8.43267 17.9791 8.44503 17.9063L9.02346 14.4375C7.71393 14.0931 6.47597 13.5189 5.36721 12.7416L3.48846 16.0313C3.41387 16.1608 3.29088 16.2554 3.14653 16.2943C3.00219 16.3331 2.84832 16.313 2.71878 16.2384C2.58924 16.1638 2.49463 16.0408 2.45578 15.8965C2.41692 15.7522 2.437 15.5983 2.51159 15.4688L4.46628 12.0488C3.77088 11.462 3.13328 10.8099 2.56221 10.1016C2.46848 9.98545 2.42471 9.83685 2.44053 9.68847C2.45635 9.54008 2.53047 9.40405 2.64659 9.31031C2.7627 9.21658 2.9113 9.17281 3.05969 9.18863C3.20808 9.20445 3.3441 9.27858 3.43784 9.39469C5.0194 11.3541 7.78784 13.6875 12 13.6875C16.2122 13.6875 18.9807 11.3541 20.5622 9.39657C20.656 9.28045 20.792 9.20633 20.9404 9.1905C21.0888 9.17468 21.2373 9.21845 21.3535 9.31219C21.4696 9.40593 21.5437 9.54195 21.5595 9.69034C21.5753 9.83873 21.5316 9.98732 21.4378 10.1034C20.8668 10.8118 20.2292 11.4638 19.5338 12.0506L21.4885 15.4688Z"
        fill={color}
      />
    </svg>
  )
}

EyeClosedIcon.displayName = 'EyeClosedIcon'
export default EyeClosedIcon;