import React from 'react';

type HomeIconPropsI = {
  className?: string;
  size?: number;
};

const HomeIcon = ({ className = '', size = 24 }: HomeIconPropsI) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M23 9v2h-2v7a3 3 0 01-3 3h-4v-6h-4v6H6a3 3 0 01-3-3v-7H1V9l11-7 5 3.18V2h3v5.09L23 9z" />
  </svg>
);

export default HomeIcon;
