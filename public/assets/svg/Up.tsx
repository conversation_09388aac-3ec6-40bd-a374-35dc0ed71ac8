import React from 'react';

type UpIconProps = {
    size?: number;
    color?: string;
    className?: string;
}

const UpIcon: React.FC<UpIconProps> = ({
    size = 18,
    color = '#448600',
    className = '',
}) => {
    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
        >
            <path
                d="M16.2701 8.65261C16.2275 8.7554 16.1554 8.84327 16.0629 8.90509C15.9704 8.96692 15.8617 8.99993 15.7504 8.99996H12.9379V14.625C12.9379 14.9233 12.8194 15.2095 12.6084 15.4205C12.3975 15.6314 12.1113 15.75 11.8129 15.75H6.18794C5.88957 15.75 5.60343 15.6314 5.39245 15.4205C5.18147 15.2095 5.06294 14.9233 5.06294 14.625V8.99996H2.25044C2.13913 9.00004 2.03029 8.9671 1.9377 8.9053C1.84512 8.8435 1.77295 8.75561 1.73034 8.65277C1.68773 8.54994 1.67659 8.43677 1.69833 8.32759C1.72007 8.21842 1.77372 8.11815 1.85247 8.03949L8.60247 1.28949C8.65472 1.23719 8.71675 1.1957 8.78504 1.16739C8.85333 1.13908 8.92652 1.12451 9.00044 1.12451C9.07436 1.12451 9.14756 1.13908 9.21585 1.16739C9.28413 1.1957 9.34617 1.23719 9.39841 1.28949L16.1484 8.03949C16.2271 8.1182 16.2806 8.21846 16.3022 8.3276C16.3239 8.43673 16.3127 8.54984 16.2701 8.65261Z"
                fill={color}
            />
        </svg>
    );
};

export default UpIcon;