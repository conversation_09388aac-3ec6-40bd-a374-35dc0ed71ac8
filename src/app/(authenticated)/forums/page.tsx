'use client';
import { DashboardLayout } from '@/components';
import useQuestions from '@/hooks/useQuestions';
import useAuth from '@/hooks/useAuth';
import React, { useEffect } from 'react';
import { ApiQuestionI } from '@/types';
import { MOCK_USER } from '@/constants';

const ForumPage = () => {
  const { isAuthenticated } = useAuth();
  const { hasMore, loadMoreQuestions, isLoading, getInitialQuestions, error } =
    useQuestions(isAuthenticated);
  const [questions, setQuestions] = React.useState<ApiQuestionI[]>([]);
  const [initialLoading, setInitialLoading] = React.useState(true);

  // Load initial questions when component mounts or auth state changes
  useEffect(() => {
    const loadInitial = async () => {
      try {
        setInitialLoading(true);
        const initialQuestions = await getInitialQuestions();
        setQuestions(initialQuestions);
      } catch (error) {
        console.error('Failed to load initial questions:', error);
      } finally {
        setInitialLoading(false);
      }
    };

    loadInitial();
  }, [getInitialQuestions]);

  // Handle loading more questions - convert page number to cursor date
  const handleLoadMore = async (_page?: number): Promise<ApiQuestionI[]> => {
    try {
      // For cursor-based pagination, we don't use page numbers
      // The loadMoreQuestions function will handle the cursor internally
      const newQuestions = await loadMoreQuestions();
      setQuestions(prev => [...prev, ...newQuestions]);
      return newQuestions;
    } catch (error) {
      console.error('Failed to load more questions:', error);
      throw error;
    }
  };

  if (initialLoading) {
    return (
      <DashboardLayout
        user={undefined}
        initialQuestions={[]}
        isLoading={true}
        onLoadMoreQuestions={handleLoadMore}
        hasMoreQuestions={false}
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Something went wrong
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout
      user={MOCK_USER}
      initialQuestions={questions}
      isLoading={isLoading}
      onLoadMoreQuestions={handleLoadMore}
      hasMoreQuestions={hasMore}
    />
  );
};

export default ForumPage;
