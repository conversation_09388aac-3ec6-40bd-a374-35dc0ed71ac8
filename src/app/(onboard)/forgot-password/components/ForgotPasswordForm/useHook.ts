import { useState } from 'react';
import { IForgotPasswordFormDataI, IForgotPasswordFormErrorsI } from './types';

const useForgotPasswordForm = () => {
  const [formData, setFormData] = useState<IForgotPasswordFormDataI>({
    email: '',
  });

  const [errors, setErrors] = useState<IForgotPasswordFormErrorsI>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateEmail = (email: string): string | undefined => {
    if (!email) {
      return 'Email or phone number is required';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

    if (!emailRegex.test(email) && !phoneRegex.test(email)) {
      return 'Please enter a valid email address or phone number';
    }

    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: IForgotPasswordFormErrorsI = {};

    const emailError = validateEmail(formData.email);
    if (emailError) {
      newErrors.email = emailError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    if (errors[name as keyof IForgotPasswordFormErrorsI]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      console.log('Forgot password request:', { email: formData.email });

      await new Promise(resolve => setTimeout(resolve, 2000));

      setIsSubmitted(true);
      console.log('Forgot password request sent successfully');
    } catch (error) {
      console.error('Forgot password error:', error);
      setErrors({
        general: 'Failed to send reset email. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    formData,
    errors,
    isLoading,
    isSubmitted,
    handleInputChange,
    handleSubmit,
  };
};

export default useForgotPasswordForm;
