'use server';

import { signIn } from '../../../../../../auth';

export const handleCredentialLogin = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  try {
    const response = await signIn('credentials', {
      type: 'EMAIL_PASSWORD',
      email,
      password,
      callbackUrl: '/forums',
      redirect: false,
    });
    return response;
  } catch (err) {
    return console.log(err);
  }
};
