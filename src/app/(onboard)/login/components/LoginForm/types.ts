import { EmailSchema, PasswordSchema } from '@/auth/validation';
import { z } from 'zod';

export const LoginFormSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
  rememberMe: z.boolean().optional(),
});

export type LoginFormDataI = z.infer<typeof LoginFormSchema>;

export type FormDataI = {
  email: string;
  password: string;
};

export type FormErrorsI = {
  email?: string;
  password?: string;
  general?: string;
};
