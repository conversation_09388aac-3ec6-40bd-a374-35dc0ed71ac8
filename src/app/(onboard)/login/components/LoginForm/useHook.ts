'use client';
import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoginFormSchema, LoginFormDataI } from './types';
import { signIn } from '../../../../../../auth';
import { handleCredentialLogin } from './action';
import { fetchProfileAPI } from '@/networks/user/profile';
import useAuth from '@/hooks/useAuth';

const useLoginForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [generalError, setGeneralError] = useState<string>('');
  const { user } = useAuth();

  const form = useForm<LoginFormDataI>({
    resolver: zodResolver(LoginFormSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
    mode: 'onChange',
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
    clearErrors,
    watch,
  } = form;

  useEffect(() => {
    if (typeof window !== 'undefined' && (window as any).AppleID) {
      (window as any).AppleID.auth.init({
        clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID!,
        scope: 'name email',
        redirectURI: process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI!,
        state: 'origin:web',
        usePopup: true,
      });
    }
  }, []);

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const onSubmit = useCallback(
    async (data: LoginFormDataI) => {
      setIsLoading(true);
      setGeneralError('');
      clearErrors();

      try {
        const result = await handleCredentialLogin({
          email: data.email,
          password: data.password,
        });

        if (!result) {
          setGeneralError('Invalid email or password. Please try again.');
        } else if (!!result && user?.id) {
          const profileData = fetchProfileAPI(user.id);
          console.log(profileData, 'profileData');
          // window.location.href = '/forums';
        }
      } catch (error) {
        console.error('Login error:', error);
        setGeneralError('Invalid email or password. Please try again.');
      }

      setIsLoading(false);
    },
    [clearErrors]
  );

  const handleAppleLogin = useCallback(async () => {
    setIsLoading(true);
    setGeneralError('');
    try {
      const response = await (window as any).AppleID.auth.signIn();
      if (response?.authorization?.id_token) {
        await signIn('credentials', {
          type: 'APPLE',
          externalToken: response.authorization.id_token,
          callbackUrl: '/forums',
        });
      } else {
        setGeneralError('Apple sign-in failed. Please try again.');
      }
    } catch {
      setGeneralError('An error occurred during Apple sign-in.');
    }
    setIsLoading(false);
  }, []);

  return {
    // React Hook Form methods
    register,
    handleSubmit: handleSubmit(onSubmit),
    formState: { errors, isValid },
    watch,

    // Custom state and methods
    isLoading,
    showPassword,
    generalError,
    togglePasswordVisibility,
    handleAppleLogin,

    // Form utilities
    setError,
    clearErrors,
  };
};

export default useLoginForm;
