import GoogleIcon from '@assets/svg/google';
import {  handleGoogleLogin } from './action';

const SocialLogin = () => {
  return (
    <div className="space-y-4">
      <form action={handleGoogleLogin}>
        <button
          type="submit"
          className="w-full flex justify-center items-center py-1 border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
        >
          <GoogleIcon />
          <span className="ml-2">Continue with Google</span>
        </button>
      </form>

      {/* <form action={handleAppleLogin}>
        <button
          type="submit"
          className="w-full flex justify-center items-center py-3 border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
        >
          <AppleIcon />
          <span className="ml-2">Sign in with Apple</span>
        </button>
      </form> */}
    </div>
  );
};

export default SocialLogin;