'use client';
import { Button } from '@/components';
import OtpInput from 'react-otp-input';
import useOtpForm from './useHook';

const OtpForm = () => {
  const {
    handleSubmit,
    errors,
    isLoading,
    email,
    type,
    countdown,
    canResend,
    handleResendOTP,
    otpValue,
    handleOtpChange,
  } = useOtpForm();

  const getTitle = () => {
    return type === 'EMAIL_VERIFICATION'
      ? 'Verify Your Email'
      : 'Reset Your Password';
  };

  const getDescription = () => {
    return type === 'EMAIL_VERIFICATION'
      ? `We've sent a 6-digit verification code to ${email}. Please enter it below to verify your email address.`
      : `We've sent a 6-digit code to ${email}. Please enter it below to reset your password.`;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-semibold text-black mb-2">{getTitle()}</h1>
        <p className="text-gray-600 text-sm">{getDescription()}</p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Enter 6-digit code
          </label>
          <div className="flex justify-center">
            <OtpInput
              value={otpValue}
              onChange={handleOtpChange}
              numInputs={6}
              renderInput={props => <input {...props} />}
              renderSeparator={<span></span>}
              // inputType='number'

              inputStyle={{
                width: '3rem',
                height: '3rem',
                margin: '0 0.25rem',
                fontSize: '1.5rem',
                borderRadius: '0.5rem',
                border: '1px solid #e5e7eb',
                textAlign: 'center',
                outline: 'none',
                transition: 'border-color 0.2s',
              }}
              // isInputNum
              shouldAutoFocus
            />
          </div>
          {errors.otp && (
            <p className="mt-2 text-sm text-red-600 text-center">
              {errors.otp.message}
            </p>
          )}
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Didn't receive the code?{' '}
            {canResend ? (
              <button
                type="button"
                onClick={handleResendOTP}
                className="text-primary font-semibold hover:underline"
              >
                Resend Code
              </button>
            ) : (
              <span className="text-gray-400">Resend in {countdown}s</span>
            )}
          </p>
        </div>

        {errors.root && (
          <div className="text-center">
            <p
              className={`text-sm ${
                errors.root.message?.includes('sent successfully')
                  ? 'text-green-600'
                  : 'text-red-600'
              }`}
            >
              {errors.root.message}
            </p>
          </div>
        )}

        <div className="space-y-4">
          <Button
            className="w-full rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            type="submit"
            disabled={isLoading || otpValue.length !== 6}
          >
            {isLoading ? 'Verifying...' : 'Verify Code'}
          </Button>

          <div className="text-center">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="text-gray-600 hover:text-gray-800 text-sm font-medium"
            >
              Back
            </button>
          </div>
        </div>
      </div>
    </form>
  );
};

export default OtpForm;
