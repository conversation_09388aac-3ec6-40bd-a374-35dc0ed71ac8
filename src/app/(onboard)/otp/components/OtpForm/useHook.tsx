import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { OtpFormData, OtpType } from './types';
import {
  verifyOTPForEmailVerification,
  verifyOTPForPasswordReset,
  sendOTPForEmailVerification,
  sendOTPForPasswordReset,
} from '@/networks/auth/otp';

const otpSchema = z.object({
  otp: z.string().min(6, 'OTP must be 6 digits').max(6, 'OTP must be 6 digits'),
});

const useOtpForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const email = searchParams.get('email') || '';
  const profileId = searchParams.get('profileId') || '';
  const type = (searchParams.get('type') as OtpType) || 'EMAIL_VERIFICATION';

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    setValue,
    watch,
  } = useForm<OtpFormData>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: '',
    },
  });

  const otpValue = watch('otp');

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const onSubmit = async (data: { otp: string }) => {
    try {
      if (type === 'EMAIL_VERIFICATION') {
        await verifyOTPForEmailVerification({
          otp: data.otp,
          profileId: profileId!,
        });
        router.push('/login?verified=true');
      } else if (type === 'PASSWORD_RESET') {
        await verifyOTPForPasswordReset({
          otp: data.otp,
          email: email,
        });
        router.push(
          `/reset-password?email=${encodeURIComponent(email)}&otp=${data.otp}`
        );
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      setError('root', {
        type: 'manual',
        message:
          error instanceof Error
            ? error.message
            : 'Invalid OTP. Please try again.',
      });
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;

    try {
      if (type === 'EMAIL_VERIFICATION' && profileId) {
        await sendOTPForEmailVerification({ profileId });
      } else if (type === 'PASSWORD_RESET' && email) {
        await sendOTPForPasswordReset({ email });
      }

      setCountdown(60);
      setCanResend(false);
      setError('root', {
        type: 'manual',
        message: 'OTP sent successfully!',
      });
    } catch (error) {
      console.error('Resend OTP error:', error);
      setError('root', {
        type: 'manual',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to resend OTP. Please try again.',
      });
    }
  };

  const handleOtpChange = (value: string) => {
    setValue('otp', value);
  };

  return {
    register,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    isLoading: isSubmitting,
    email,
    type,
    countdown,
    canResend,
    handleResendOTP,
    otpValue,
    handleOtpChange,
  };
};

export default useOtpForm;
