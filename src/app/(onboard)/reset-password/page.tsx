import Link from 'next/link';

const ResetPasswordPage = () => {
  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md lg:shadow-lg lg:p-8 rounded-sm">
          <div className="text-center">
            <h1 className="text-3xl font-semibold text-black mb-2">
              Reset Password
            </h1>
            <p className="text-gray-600 text-sm mb-6">
              Enter your new password below
            </p>
          </div>

          {/* Reset Password Form would go here */}
          <div className="text-center">
            <p className="text-gray-600">Reset password form coming soon...</p>
          </div>
        </div>

        <div className="text-center mt-6 space-y-2">
          <p className="text-sm text-gray-600">
            Remember your password?{' '}
            <Link
              href="/login"
              className="text-primary font-semibold hover:underline"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
