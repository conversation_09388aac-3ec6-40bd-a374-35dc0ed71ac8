'use client';
import { Button, Input } from '@/components';
import EyeClosedIcon from '@assets/svg/EyeClosed';
import EyeIcon from '@assets/svg/Eye';
import useSignUpForm from './useHook';

export function SignUpForm() {
  const {
    register,
    handleSubmit,
    errors,
    isLoading,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    rememberMe,
    setRememberMe,
  } = useSignUpForm();

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Input
          id="email"
          type="text"
          autoComplete="username"
          placeholder="Email or phone number"
          className="w-full"
          {...register('email')}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>
      <div>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="new-password"
            placeholder="New Password"
            className="w-full pr-16"
            {...register('password')}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={togglePasswordVisibility}
          >
            {showPassword ? (
              <EyeClosedIcon
                className="text-gray-400 hover:text-gray-600"
                width={20}
                height={20}
              />
            ) : (
              <EyeIcon
                className="text-gray-400 hover:text-gray-600"
                width={20}
                height={20}
              />
            )}
          </button>
        </div>
        {errors.password && (
          <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
        )}
      </div>

      <div>
        <div className="relative">
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            autoComplete="new-password"
            placeholder="Confirm Password"
            className="w-full pr-16"
            {...register('confirmPassword')}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={toggleConfirmPasswordVisibility}
          >
            {showConfirmPassword ? (
              <EyeClosedIcon
                className="text-gray-400 hover:text-gray-600"
                width={20}
                height={20}
              />
            ) : (
              <EyeIcon
                className="text-gray-400 hover:text-gray-600"
                width={20}
                height={20}
              />
            )}
          </button>
        </div>
        {errors.confirmPassword && (
          <p className="mt-1 text-sm text-red-600">
            {errors.confirmPassword.message}
          </p>
        )}
      </div>

      <div className="flex items-start space-x-2">
        <input
          id="remember-me"
          name="remember-me"
          type="checkbox"
          checked={rememberMe}
          onChange={e => setRememberMe(e.target.checked)}
          className="h-4 w-4 text-[#0077B5] focus:ring-[#0077B5] border-gray-300 rounded mt-0.5 accent-primary"
        />
        <label htmlFor="remember-me" className="text-sm text-gray-700">
          Remember me
        </label>
      </div>

      {errors.root && (
        <p className="text-sm text-red-600">{errors.root.message}</p>
      )}

      <div className="w-full">
        <Button
          className="w-full rounded-full bg-[#0077B5] hover:bg-[#005885] text-white text-base font-semibold py-3"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Creating account...' : 'Agree & Join'}
        </Button>
      </div>
    </form>
  );
}
