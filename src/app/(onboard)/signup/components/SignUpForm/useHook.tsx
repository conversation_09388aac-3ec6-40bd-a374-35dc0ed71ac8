'use client'
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { SignUpFormDataI } from './types';
import { registerAPI } from '@/networks/auth/register';
import { navigateToEmailVerification } from '@/auth/navigation';
import { PasswordSchema, EmailSchema } from '@/auth/validation';
import { sendOTPForEmailVerificationAPI } from '@/networks/auth/otp';

const signUpSchema = z
  .object({
    email: EmailSchema,
    password: PasswordSchema,
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

const useSignUpForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<SignUpFormDataI>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      isPPAndTNCAccepted: false,
    },
  });

  const onSubmit = async (data: SignUpFormDataI) => {
    try {
      const result = await registerAPI({
        type: 'EMAIL_PASSWORD',
        email: data.email,
        password: data.password,
        confirmPassword: data.confirmPassword,
        isPPAndTNCAccepted: true,
      });
      if (typeof window !== 'undefined') {
        localStorage.setItem('token', result.token);
        localStorage.setItem('userProfileId', result.profileId);
      }
      try {
        await sendOTPForEmailVerificationAPI(result.profileId);
        navigateToEmailVerification(data.email, result.profileId);

      } catch {
        showToast({
          type: 'error',
          message: 'Account Created',
          description:
            'Account created but failed to send verification email. Please try again later.',
        });
      }

    } catch (error) {
      console.error('Registration error:', error);
      setError('root', {
        type: 'manual',
        message:
          error instanceof Error
            ? error.message
            : 'Registration failed. Please try again.',
      });
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(prev => !prev);
  };

  return {
    register,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    isLoading: isSubmitting,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    rememberMe,
    setRememberMe,
  };
};

export default useSignUpForm;
