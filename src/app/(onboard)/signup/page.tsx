'use client';

import Link from 'next/link';
import { SignUpForm } from './components/SignUpForm';

export default function SignUpPage() {
  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-sm lg:shadow-lg lg:p-6 rounded-sm">
          <div className="mb-6">
            <h1 className="text-3xl font-semibold text-black mb-6">Sign Up</h1>
          </div>

          <div className="space-y-4">
            <SignUpForm />

            {/* <SocialSignUp /> */}
          </div>
        </div>

        <div className="text-center mt-6 space-y-2">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              href="/login"
              className="text-primary font-semibold hover:underline"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
