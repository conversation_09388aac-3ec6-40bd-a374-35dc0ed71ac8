import { loginAPI } from '@/networks/auth/login';
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text', placeholder: 'jsmith' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials, _req) {
        if (!credentials) return null;

        const { email, password } = credentials as {
          type: string;
          email?: string;
          password?: string;
          externalToken?: string;
        };

        const loginResponse = await login<PERSON><PERSON>({
          type: 'EMAIL_PASSWORD',
          email,
          password,
        });

        if (!loginResponse?.token) throw new Error('Invalid credentials');
        return loginResponse;
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
  ],
};

export default NextAuth(authOptions);
