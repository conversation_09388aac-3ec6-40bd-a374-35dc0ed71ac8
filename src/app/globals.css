@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --primary: #448600;
  --primary-hover: #3a7300;
  --secondary: #666666;
  --text: #000000;
  --text-light: #666666;
  --border: #d0d7de;
  --background: #ffffff;
  --background-light: #f8f9fa;
  --ring-primary: #448600;
}

@theme {
  --color-primary: #448600;
  --color--ring-primary: #448600;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe U<PERSON>', <PERSON><PERSON>, 'Helvetica Neue',
    Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
*:focus {
  outline: 2px solid #448600;
  outline-offset: 2px;
}

/* Button focus styles */
button:focus {
  outline: 2px solid #448600;
  outline-offset: 2px;
}

/* Custom animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Responsive utilities */
@media (max-width: 1024px) {
  .lg\:col-span-3 {
    display: none;
  }

  .lg\:col-span-6 {
    grid-column: span 12;
  }
}

@media (max-width: 768px) {
  .sticky {
    position: relative !important;
    top: auto !important;
  }
}
