import { OtpType } from '@/app/(onboard)/otp/components/OtpForm/types';

export type OtpNavigationParams = {
  email: string;
  profileId?: string;
  type: OtpType;
};

/**
 * Generates the OTP page URL with the required parameters
 */
export const generateOtpUrl = (params: OtpNavigationParams): string => {
  const searchParams = new URLSearchParams({
    email: params.email,
    type: params.type,
  });

  if (params.profileId) {
    searchParams.set('profileId', params.profileId);
  }

  return `/otp?${searchParams.toString()}`;
};

/**
 * Navigates to the OTP page with the specified parameters
 */
export const navigateToOtp = (params: OtpNavigationParams): void => {
  const url = generateOtpUrl(params);
  window.location.href = url;
};

/**
 * Navigates to email verification OTP page
 */
export const navigateToEmailVerification = (
  email: string,
  profileId: string
): void => {
  navigateToOtp({
    email,
    profileId,
    type: 'EMAIL_VERIFICATION',
  });
};

/**
 * Navigates to password reset OTP page
 */
export const navigateToPasswordReset = (email: string): void => {
  navigateToOtp({
    email,
    type: 'PASSWORD_RESET',
  });
};




