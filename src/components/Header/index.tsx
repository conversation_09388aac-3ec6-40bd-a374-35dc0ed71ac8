'use client';

import React from 'react';
import Link from 'next/link';
import Logo from '../Logo';
import { CONFIG } from '../../constants/config';
import ForumIcon from '@assets/svg/Forum';

const Header = () => {
  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm"
      style={{ height: CONFIG.layout.headerHeight }}
    >
      <div className="max-w-screen-xl mx-auto px-4 h-full">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Logo compact={false} />
            </Link>
          </div>

          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center justify-center w-12 h-12 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              aria-label="Home"
            >
              <ForumIcon
                size={24}
                className="text-gray-600 hover:text-gray-900 transition-colors duration-200"
              />
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
