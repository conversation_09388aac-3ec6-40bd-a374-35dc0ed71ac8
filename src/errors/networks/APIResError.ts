/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ErrorCodeI } from '../consts';
import APIError from './AppError';
import { APIResErrorI } from './types';

class APIResError extends APIError {
  private _status: number;
  private _code: ErrorCodeI;
  constructor(status: number, apiResError: APIResErrorI) {
    super(apiResError.message, apiResError?.error);
    this._status = status;
    this._code = apiResError.code;
  }
  public get status(): number {
    return this._status;
  }
  public get code(): ErrorCodeI {
    return this._code;
  }
}

export default APIResError;
