/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ObjUnknownI } from '@/types/common/data';

class AppError extends Error {
  private _error?: ObjUnknownI | unknown;
  constructor(message: string, error?: ObjUnknownI | unknown) {
    super(message);
    this._error = error;
  }
  public get error(): ObjUnknownI | unknown | undefined {
    return this._error;
  }
}

export default AppError;
