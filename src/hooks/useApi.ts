'use client';

import { api<PERSON>all, MethodI, APICallI } from '@/lib/api';
import { useState, useCallback } from 'react';

type UseApiStateI<T> = {
  data: T | null;
  loading: boolean;
  error: string | null;
};

type UseApiReturnI<T> = UseApiStateI<T> & {
  execute: (options?: Omit<APICallI, 'payload'>) => Promise<T>;
  reset: () => void;
};

export const useApi = <PayloadT = unknown, ResponseT = unknown>(
  path: string,
  method: MethodI,
  defaultPayload?: PayloadT
): UseApiReturnI<ResponseT> => {
  const [state, setState] = useState<UseApiStateI<ResponseT>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (
      options: Omit<APICallI<PayloadT>, 'payload'> = {}
    ): Promise<ResponseT> => {
      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        const result = await apiCall<PayloadT, ResponseT>(path, method, {
          payload: defaultPayload,
          ...options,
        });

        setState(prev => ({ ...prev, data: result, loading: false }));
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'An error occurred';
        setState(prev => ({ ...prev, error: errorMessage, loading: false }));
        throw error;
      }
    },
    [path, method, defaultPayload]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
};

export default useApi;
