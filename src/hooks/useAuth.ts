'use client';

import { useSession } from 'next-auth/react';
import { UserI } from '@/types';

type UseAuthReturnI = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserI | null;
  session: any;
};

const useAuth = (): UseAuthReturnI => {
  const { data: session, status } = useSession();

  const isLoading = status === 'loading';
  const isAuthenticated = status === 'authenticated' && !!session?.user;

  // Convert NextAuth user to our UserI type
  const user: UserI | null = session?.user
    ? {
        id: session.user.backendData?.profileId || session.user.id || '',
        name: session.user.name || session.user.backendData?.name || '',
        email: session.user.email || session.user.backendData?.email || '',
        avatar: session.user.image || session.user.backendData?.avatar || '',
        title: '', // Not available in AuthLoginResultI
        company: '', // Not available in AuthLoginResultI
        location: '', // Not available in AuthLoginResultI
        connections: 0, // Not available in AuthLoginResultI
        profileViews: 0, // Not available in AuthLoginResultI
        postImpressions: 0, // Not available in AuthLoginResultI
        isVerified: false, // Not available in AuthLoginResultI
        bio: '', // Not available in AuthLoginResultI
      }
    : null;

  return {
    isAuthenticated,
    isLoading,
    user,
    session,
  };
};

export default useAuth;
