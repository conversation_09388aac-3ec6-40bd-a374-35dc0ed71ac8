'use client';

import { useState, useEffect } from 'react';
import {
  getOrGenerateDeviceId,
  setDeviceId,
  clearDeviceId,
} from '@/utils/deviceId';

type UseDeviceIdReturnI = {
  deviceId: string | null;
  isLoading: boolean;
  refreshDeviceId: () => void;
  setCustomDeviceId: (id: string) => void;
  clearStoredDeviceId: () => void;
};

/**
 * React hook for managing device ID in components
 * Provides access to the current device ID and utilities to manage it
 */
export const useDeviceId = (): UseDeviceIdReturnI => {
  const [deviceId, setDeviceIdState] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize device ID on mount
    const initializeDeviceId = () => {
      try {
        const id = getOrGenerateDeviceId();
        setDeviceIdState(id);
      } catch (error) {
        console.error('Error initializing device ID:', error);
        setDeviceIdState(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeDeviceId();
  }, []);

  const refreshDeviceId = () => {
    try {
      const id = getOrGenerateDeviceId();
      setDeviceIdState(id);
    } catch (error) {
      console.error('Error refreshing device ID:', error);
    }
  };

  const setCustomDeviceId = (id: string) => {
    try {
      setDeviceId(id);
      setDeviceIdState(id);
    } catch (error) {
      console.error('Error setting custom device ID:', error);
    }
  };

  const clearStoredDeviceId = () => {
    try {
      clearDeviceId();
      setDeviceIdState(null);
      // Generate a new one immediately
      refreshDeviceId();
    } catch (error) {
      console.error('Error clearing device ID:', error);
    }
  };

  return {
    deviceId,
    isLoading,
    refreshDeviceId,
    setCustomDeviceId,
    clearStoredDeviceId,
  };
};

export default useDeviceId;
