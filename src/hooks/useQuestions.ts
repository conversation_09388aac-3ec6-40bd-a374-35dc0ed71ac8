'use client';

import { useState, useCallback } from 'react';
import { ApiQuestionI, ForumQuestionListingI } from '../types';
import {
  fetchQuestionsListingAPI,
  fetchQuestionsListingPublicAPI,
} from '../networks/forum/globalSearch';

const useQuestions = (isAuthenticated: boolean = true) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nextCursorDate, setNextCursorDate] = useState<Date | null>(null);

  // Convert API response to our ApiQuestionI format
  const convertApiQuestion = (item: ForumQuestionListingI): ApiQuestionI => {
    return {
      ...item,
      isAuthenticated,
      isTruncated: !isAuthenticated,
    };
  };

  const loadMoreQuestions = useCallback(
    async (cursorDate?: Date | null): Promise<ApiQuestionI[]> => {
      setIsLoading(true);
      setError(null);
      try {
        const apiFunction = isAuthenticated
          ? fetchQuestionsListingAPI
          : fetchQuestionsListingPublicAPI;
        const response = await apiFunction({
          cursorDate: cursorDate || nextCursorDate,
          pageSize: 10,
          type: 'ALL',
        });

        const questions = response.data.map(convertApiQuestion);

        // Update cursor for next page
        setNextCursorDate(response.nextCursorDate);

        if (questions.length === 0 || !response.nextCursorDate) {
          setHasMore(false);
        }

        return questions;
      } catch (err) {
        console.error('Failed to load questions:', err);
        setError('Failed to load questions. Please try again.');
        setHasMore(false);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [isAuthenticated, convertApiQuestion, nextCursorDate]
  );

  const getInitialQuestions = useCallback(async (): Promise<ApiQuestionI[]> => {
    setIsLoading(true);
    setError(null);
    try {
      // Reset cursor for initial load
      setNextCursorDate(null);
      const questions = await loadMoreQuestions(null);
      return questions;
    } catch (error) {
      console.error('Failed to load initial questions:', error);
      setError('Failed to load questions. Please check your connection.');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [loadMoreQuestions]);

  return {
    isLoading,
    hasMore,
    error,
    loadMoreQuestions,
    getInitialQuestions,
  };
};

export default useQuestions;
