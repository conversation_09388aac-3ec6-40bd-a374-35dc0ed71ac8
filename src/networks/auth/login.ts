import { AuthLoginBodyI, AuthLoginResultI } from './types';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export const loginAPI = async (
  payload: AuthLoginBodyI
): Promise<AuthLoginResultI> => {
  try {
    const url = `${BASE_URL}/backend/api/v1/auth/login`;

    const getDeviceId = (): string => {
      if (typeof window === 'undefined') return '';

      let deviceId = localStorage.getItem('deviceId');
      if (!deviceId) {
        deviceId = crypto.randomUUID();
        localStorage.setItem('deviceId', deviceId);
      }
      return deviceId;
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
      'x-device-id': getDeviceId(),
      'x-platform': 'web_app',
      'x-version-no': process.env.NEXT_PUBLIC_VERSION_NO || '',
    };

    const options: RequestInit = {
      method: 'POST',
      headers,
      body: JSON.stringify(payload),
      credentials: 'include',
    };

    const response = await fetch(url, options);

    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    if (!response.ok) {
      const errorData = isJson
        ? await response.json()
        : { message: 'Request failed' };
      throw new Error(
        `HTTP ${response.status}: ${errorData.message || 'Unknown error'}`
      );
    }

    const result = isJson ? await response.json() : null;
    return result as AuthLoginResultI;
  } catch (error) {
    console.error('Login API Error:', error);
    throw error;
  }
};
