import { apiCall } from '@/lib/api';
import { SendOTPForEmailVerificationBodyI, VerifyOTPForEmailVerificationBodyI } from './types';

export const sendOTPForEmailVerificationAPI = async (payload: SendOTPForEmailVerificationBodyI) => {
  const result = await apiCall<SendOTPForEmailVerificationBodyI, undefined>(
    '/backend/api/v1/secure-auth/email/otp',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const verifyOTPForEmailVerificationAPI = async (
  payload: VerifyOTPForEmailVerificationBodyI,
) => {
  const result = await apiCall<VerifyOTPForEmailVerificationBodyI, unknown>(
    '/backend/api/v1/secure-auth/email/otp',
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
