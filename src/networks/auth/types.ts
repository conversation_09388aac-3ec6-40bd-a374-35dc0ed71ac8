export type AuthTypeI = 'APPLE' | 'GOOGLE' | 'EMAIL_PASSWORD';
export type AuthLoginBodyI = {
  email?: string;
  password?: string;
  type: AuthTypeI;
  externalToken?: string;
  deviceToken?: string;
};

export type AuthLoginResultI = {
  name: string;
  username: string;
  email: string;
  profileId: string;
  avatar: string | null;
  isUsernameSaved: boolean;
  isEmailVerified: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  isPrivacyPolicyAccepted: boolean;
  token: string;
  jwtToken: string;
  previousStatus:
    | 'ACTIVE'
    | 'INACTIVE'
    | 'SCHEDULED_FOR_DELETION'
    | 'BLOCKED'
    | 'DELETED';
};

export type AuthRegisterBodyI = {
  type: 'GOOGLE' | 'EMAIL_PASSWORD';
  email?: string;
  password?: string;
  confirmPassword?: string;
  isPPAndTNCAccepted: boolean;
};

export type AuthRegisterResultI = {
  profileId: string;
  token: string;
};

export type SendOTPForEmailVerificationBodyI = {
  profileId: string;
};

export type VerifyOTPForEmailVerificationBodyI = {
  otp: string;
  profileId: string;
};

export type SendOTPForPasswordResetBodyI = {
  email: string;
};

export type VerifyOTPForPasswordResetBodyI = {
  email: string;
  otp: string;
};

export type ResetPasswordBodyI = {
  email: string;
  newPassword: string;
};
