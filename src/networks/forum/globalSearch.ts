import { apiCall } from '@/lib/api';
import {
  ForumQuestionListingInputI,
  ForumQuestionListingResponseI,
} from '@/types';

export const fetchQuestionsListingAPI = async (
  params: ForumQuestionListingInputI
): Promise<ForumQuestionListingResponseI> => {
  const result = await apiCall<undefined, ForumQuestionListingResponseI>(
    'backend/api/v1/forum/questions-listing',
    'GET',
    {
      isAuth: true,
      query: params,
    }
  );

  return result;
};

// For unauthenticated users - this will return truncated content
export const fetchQuestionsListingPublicAPI = async (
  params: ForumQuestionListingInputI
): Promise<ForumQuestionListingResponseI> => {
  const result = await apiCall<undefined, ForumQuestionListingResponseI>(
    'backend/api/v1/forum/questions-listing',
    'GET',
    {
      isAuth: false,
      query: params,
    }
  );

  return result;
};
