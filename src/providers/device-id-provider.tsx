'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getOrGenerateDeviceId } from '@/utils/deviceId';

type DeviceIdContextI = {
  deviceId: string | null;
  isLoading: boolean;
  error: string | null;
};

const DeviceIdContext = createContext<DeviceIdContextI>({
  deviceId: null,
  isLoading: true,
  error: null,
});

type DeviceIdProviderPropsI = {
  children: React.ReactNode;
};

/**
 * Provider that ensures device ID is initialized early in the app lifecycle
 * This helps prevent issues with server-side rendering and ensures consistent device ID
 */
export const DeviceIdProvider = ({ children }: DeviceIdProviderPropsI) => {
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeDeviceId = async () => {
      try {
        // Small delay to ensure we're on client side
        await new Promise(resolve => setTimeout(resolve, 0));

        const id = getOrGenerateDeviceId();
        setDeviceId(id);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize device ID:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to initialize device ID'
        );
      } finally {
        setIsLoading(false);
      }
    };

    initializeDeviceId();
  }, []);

  return (
    <DeviceIdContext.Provider value={{ deviceId, isLoading, error }}>
      {children}
    </DeviceIdContext.Provider>
  );
};

/**
 * Hook to access device ID context
 */
export const useDeviceIdContext = (): DeviceIdContextI => {
  const context = useContext(DeviceIdContext);
  if (!context) {
    throw new Error(
      'useDeviceIdContext must be used within a DeviceIdProvider'
    );
  }
  return context;
};
