/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ComponentProps } from 'react';

export type ClassNameI = ComponentProps<'div'>['className'];

export type IdTitleI = {
  id: string;
  title: string;
};

export type IdNameI = {
  id: string;
  name: string;
};

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

type Nullable<T> = T | null;

export type UndefinedNullI = undefined | null;
export type StringUndefinedI = string | undefined;
export type StringUndefinedNullI = string | UndefinedNullI;
export type StringNullI = Nullable<string>;
export type BooleanNullI = Nullable<boolean>;
export type NumberNullI = Nullable<number>;
export type KeyStrStrI = Record<string, string>;
export type StrNullVoidFnI = (id: StringNullI) => void;
export type ObjUnknownI = Record<string, unknown>;
export type ObjStrI = Record<string, string>;
export type ObjStrNumI = Record<string, string | number>;
export type PrimitiveI = string | boolean | number;
