import 'next-auth';
import { AuthLoginResultI } from '@/networks/auth/types';

declare module 'next-auth' {
  interface User {
    backendData?: AuthLoginResultI;
  }

  interface Session {
    accessToken: string;
    jwtToken: string;
    isUsernameSaved: boolean;
    isPersonalDetailsSaved: boolean;
    isWorkDetailsSaved: boolean;
    isPrivacyPolicyAccepted: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken: string;
    jwtToken: string;
    isUsernameSaved: boolean;
    isPersonalDetailsSaved: boolean;
    isWorkDetailsSaved: boolean;
    isPrivacyPolicyAccepted: boolean;
  }
}
