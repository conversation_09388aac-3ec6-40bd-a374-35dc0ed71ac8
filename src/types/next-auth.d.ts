import 'next-auth';
import { AuthLoginResultI } from '@/networks/auth/types';

declare module 'next-auth' {
  interface User {
    backendData?: AuthLoginResultI;
    deviceId?: string;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name?: string;
      image?: string;
    };
    // Backend data
    profileId: string;
    username?: string;
    designationText?: string;
    entityText?: string;
    avatar?: string;
    isEmailVerified: boolean;
    isUsernameSaved: boolean;
    isPersonalDetailsSaved: boolean;
    isWorkDetailsSaved: boolean;
    isPrivacyPolicyAccepted: boolean;
    previousStatus: string;
    // Tokens
    token: string;
    jwtToken: string;
    // Device info
    deviceId: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    // Backend data
    profileId?: string;
    username?: string;
    designationText?: string;
    entityText?: string;
    avatar?: string;
    isEmailVerified?: boolean;
    isUsernameSaved?: boolean;
    isPersonalDetailsSaved?: boolean;
    isWorkDetailsSaved?: boolean;
    isPrivacyPolicyAccepted?: boolean;
    previousStatus?: string;
    // Tokens
    token?: string;
    jwtToken?: string;
    // Device info
    deviceId?: string;
    // Backend data object
    backendData?: AuthLoginResultI;
  }
}
