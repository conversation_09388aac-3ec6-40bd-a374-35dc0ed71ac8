/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export const capitalizeWords = (data: string): string => {
  if (!data || typeof data !== 'string') return '';

  return data
    .split(/\s+/)
    .map(word =>
      word.length
        ? `${word.charAt(0).toUpperCase()}${word.slice(1).toLowerCase()}`
        : ''
    )
    .join(' ');
};

export const generateUUIDv4 = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, char => {
    const rand = Math.floor(Math.random() * 16);
    const value = char === 'x' ? rand : (rand % 4) + 8;
    return value.toString(16);
  });
};

export const capitalizeFirstLetter = (text: string): string =>
  text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
