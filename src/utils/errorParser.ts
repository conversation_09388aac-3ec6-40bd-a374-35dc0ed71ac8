export type ApiError = {
  message: string;
  code?: string;
  details?: string;
  statusCode?: number;
};

export type ParsedError = {
  message: string;
  description?: string;
  code?: string;
};

/**
 * Parse API errors and return user-friendly messages
 */
export const parseApiError = (error: unknown): ParsedError => {
  // Default error message
  const defaultError: ParsedError = {
    message: 'Something went wrong',
    description: 'Please try again later.',
  };

  // Handle null/undefined
  if (!error) {
    return defaultError;
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      message: 'Error',
      description: error,
    };
  }

  // Handle Error objects
  if (error instanceof Error) {
    return {
      message: 'Error',
      description: error.message,
    };
  }

  // Handle API error objects
  if (typeof error === 'object' && error !== null) {
    const apiError = error as any;

    // Check for common API error structures
    if (apiError.message) {
      return {
        message: apiError.title || apiError.error || 'Error',
        description: apiError.message,
        code: apiError.code,
      };
    }

    // Check for nested error structures
    if (apiError.error) {
      if (typeof apiError.error === 'string') {
        return {
          message: 'Error',
          description: apiError.error,
        };
      }

      if (apiError.error.message) {
        return {
          message: apiError.error.title || 'Error',
          description: apiError.error.message,
          code: apiError.error.code,
        };
      }
    }

    // Check for response data
    if (apiError.response?.data) {
      return parseApiError(apiError.response.data);
    }

    // Check for status-based errors
    if (apiError.status || apiError.statusCode) {
      const status = apiError.status || apiError.statusCode;
      return {
        message: `Error ${status}`,
        description: getStatusMessage(status),
        code: status.toString(),
      };
    }

    // Try to extract any meaningful message
    const possibleMessage =
      apiError.detail ||
      apiError.details ||
      apiError.description ||
      JSON.stringify(apiError);

    if (possibleMessage && typeof possibleMessage === 'string') {
      return {
        message: 'Error',
        description: possibleMessage,
      };
    }
  }

  return defaultError;
};

/**
 * Get user-friendly message for HTTP status codes
 */
const getStatusMessage = (status: number): string => {
  switch (status) {
    case 400:
      return 'Invalid request. Please check your input.';
    case 401:
      return 'Authentication failed. Please login again.';
    case 403:
      return 'Access denied. You do not have permission.';
    case 404:
      return 'Resource not found.';
    case 409:
      return 'Conflict. The resource already exists.';
    case 422:
      return 'Validation failed. Please check your input.';
    case 429:
      return 'Too many requests. Please try again later.';
    case 500:
      return 'Server error. Please try again later.';
    case 502:
      return 'Service unavailable. Please try again later.';
    case 503:
      return 'Service temporarily unavailable.';
    default:
      return 'An unexpected error occurred.';
  }
};

/**
 * Parse authentication-specific errors
 */
export const parseAuthError = (error: unknown): ParsedError => {
  const parsed = parseApiError(error);

  // Handle common auth error codes
  if (parsed.code) {
    switch (parsed.code) {
      case 'AUTH001':
        return {
          message: 'Invalid Credentials',
          description: 'Email or password is incorrect.',
        };
      case 'AUTH002':
        return {
          message: 'Account Not Found',
          description: 'No account found with this email address.',
        };
      case 'AUTH003':
        return {
          message: 'Account Locked',
          description: 'Your account has been temporarily locked.',
        };
      case 'AUTH004':
        return {
          message: 'Email Not Verified',
          description: 'Please verify your email address first.',
        };
      case 'AUTH005':
        return {
          message: 'Account Already Exists',
          description: 'An account with this email already exists.',
        };
      case 'AUTH006':
        return {
          message: 'Invalid Token',
          description: 'Your session has expired. Please login again.',
        };
      case 'AUTH007':
        return {
          message: 'Validation Error',
          description: 'Please check your input and try again.',
        };
      case 'AUTH008':
        return {
          message: 'Registration Failed',
          description: 'Unable to create account. Please try again.',
        };
      case 'AUTH009':
        return {
          message: 'Unsupported Platform',
          description: 'This platform is not supported.',
        };
      default:
        return parsed;
    }
  }

  return parsed;
};

export default parseApiError;
