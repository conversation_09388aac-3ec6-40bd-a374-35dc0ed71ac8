import React from 'react';
import toast from 'react-hot-toast';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export type ShowToastParams = {
  type: ToastType;
  message: string;
  description?: string;
  duration?: number;
};

/**
 * Show a toast notification with custom styling
 */
export const showToast = ({
  type,
  message,
  description,
  duration = 4000,
}: ShowToastParams) => {
  const toastContent = (
    <div className="flex flex-col">
      <div className="font-semibold text-sm">{message}</div>
      {description && (
        <div className="text-xs text-gray-600 mt-1">{description}</div>
      )}
    </div>
  );

  const baseStyle = {
    borderRadius: '8px',
    padding: '10px 12px',
    fontSize: '14px',
    maxWidth: '300px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  };

  switch (type) {
    case 'success':
      return toast.success(toastContent, {
        duration,
        style: {
          ...baseStyle,
          background: '#10B981',
          color: '#ffffff',
        },
        iconTheme: {
          primary: '#ffffff',
          secondary: '#10B981',
        },
      });

    case 'error':
      return toast.error(toastContent, {
        duration,
        style: {
          ...baseStyle,
          background: '#EF4444',
          color: '#ffffff',
        },
        iconTheme: {
          primary: '#ffffff',
          secondary: '#EF4444',
        },
      });

    case 'warning':
      return toast(toastContent, {
        duration,
        icon: '⚠️',
        style: {
          ...baseStyle,
          background: '#F59E0B',
          color: '#ffffff',
        },
      });

    case 'info':
      return toast(toastContent, {
        duration,
        icon: 'ℹ️',
        style: {
          ...baseStyle,
          background: '#3B82F6',
          color: '#ffffff',
        },
      });

    default:
      return toast(toastContent, {
        duration,
        style: baseStyle,
      });
  }
};

export default showToast;
